# Database Schema Documentation

## Overview
This document outlines the database schema for the Screening Portal backend system. The system uses MongoDB with Mongoose ODM and consists of multiple interconnected entities managing funding applications, corporate sponsors, users, and various workflow processes.

## Core Entities

### 1. CorporateSponsor
**Primary Entity**: Represents organizations that provide funding
- `_id`: ObjectId (Primary Key)
- `name`: String (required)
- `description`: String
- `logo`: String
- `contactPerson`: Object (name, email, phone, position)
- `address`: Object (street, city, province, postalCode, country)
- `website`: String
- `industry`: String
- `totalFunding`: Number
- `activePrograms`: Number
- `totalBeneficiaries`: Number
- `status`: Enum ['active', 'inactive']
- `createdAt`, `updatedAt`: Date

**Relationships**:
- One-to-Many with FundingProgramme
- One-to-Many with Application
- One-to-Many with User (via corporateSponsorId)

### 2. FundingProgramme
**Primary Entity**: Represents funding programs offered by corporate sponsors
- `_id`: ObjectId (Primary Key)
- `name`: String (required)
- `description`: String
- `corporateSponsorId`: ObjectId → CorporateSponsor (required)
- `objectives`: Array[String]
- `fundingCriteria`: Array[String]
- `fundingTerms`: String
- `fundingTypes`: Array[Enum] ['Grant', 'Loan', 'Equity', 'Convertible Note', 'Other']
- `eligibilityCriteria`: Array[String]
- `geographicalCoverage`: Object (provinces, municipalities)
- `sectors`: Object (included, excluded)
- `fundingPurposes`: Array[Enum]
- `budget`: Object (totalAmount, currency, allocated, remaining)
- `timeline`: Object (startDate, endDate, applicationDeadline)
- `status`: Enum ['draft', 'active', 'paused', 'completed', 'cancelled']
- `createdBy`: ObjectId → User
- `createdAt`, `updatedAt`: Date

**Relationships**:
- Many-to-One with CorporateSponsor
- One-to-Many with Application
- One-to-Many with CommitteeMeeting
- One-to-Many with User (via fundingProgrammeId)
- One-to-Many with ProgrammeAssignment

### 3. User
**Primary Entity**: System users with various roles and permissions
- `_id`: ObjectId (Primary Key)
- `id`: String (unique, sparse)
- `username`: String (required, unique)
- `email`: String (required, unique)
- `password`: String (required, hashed)
- `firstName`, `lastName`: String
- `role`: Enum ['admin', 'analyst', 'manager', 'committee_member', 'corporate_user']
- `roles`: Array[String] (new multi-role system)
- `permissions`: Array[String]
- `organizationType`: Enum ['20/20Insight', 'CorporateSponsor', 'ServiceProvider']
- `organizationId`: String
- `corporateSponsorId`: ObjectId → CorporateSponsor
- `fundingProgrammeId`: ObjectId → FundingProgramme
- `programmeAssignments`: Array[Object] (programmeId, role)
- `phone`: String
- `profilePicture`: String
- `status`: Enum ['active', 'inactive', 'suspended']
- `isActive`: Boolean
- `lastLogin`: Date
- `resetPasswordToken`, `resetPasswordExpires`: String, Date
- `createdAt`, `updatedAt`: Date

**Relationships**:
- Many-to-One with CorporateSponsor
- Many-to-One with FundingProgramme
- One-to-Many with Application (as owner/assignee)
- One-to-Many with Report (as creator)
- One-to-Many with ChatMessage (as sender)
- Many-to-Many with ChatRoom (via ChatParticipant)

### 4. Application
**Primary Entity**: Funding applications submitted by beneficiaries
- `_id`: ObjectId (Primary Key)
- `id`: String (required, unique, auto-generated: APP-2025-XXX)
- `programmeId`: ObjectId → FundingProgramme (required)
- `corporateSponsorId`: ObjectId → CorporateSponsor (required)
- `registrationNumber`: String
- `fundingAmount`: Number
- `submissionDate`: Date
- `currentMainStage`: Enum (ApplicationMainStage)
- `currentSubStage`: Enum (ApplicationSubStage)
- `currentStatus`: Enum (ApplicationStatus)
- `owner`: String
- `personalInfo`: Object (firstName, lastName, idNumber, dateOfBirth, etc.)
- `businessInfo`: Object (businessName, registrationNumber, industry, etc.)
- `contactDetails`: Object (email, phone, address)
- `financialInfo`: Object (annualTurnover, employees, etc.)
- `bbbeeProfile`: Object (level, percentages, etc.)
- `smeInterview`: Object (date, interviewer, notes, status)
- `documents`: Array[String]
- `notes`: Array[String]
- `tags`: Array[String]
- `stageStatusAuditLog`: Array[Object] (audit trail)
- `score`: Number
- `assignedTo`: String
- `approvalWorkflow`: Object (currentStep, steps, committeeMeetings)
- `lastUpdated`: Date

**Relationships**:
- Many-to-One with FundingProgramme
- Many-to-One with CorporateSponsor
- One-to-Many with ApprovalWorkflow
- One-to-Many with SiteVisit
- One-to-Many with Scorecard
- One-to-Many with Interview
- One-to-Many with LoanOffer

## Supporting Entities

### 5. ApprovalWorkflow
**Workflow Entity**: Manages the approval process for applications
- `_id`: ObjectId (Primary Key)
- `applicationId`: ObjectId → Application (required)
- `programmeId`: ObjectId → FundingProgramme (required)
- `currentStep`: Enum (workflow steps)
- `steps`: Array[Object] (step, status, assignedTo, dates, notes)
- `committeeMeetings`: Array[Object] (meetingId, status, presentationDate)
- `createdAt`, `updatedAt`: Date

### 6. CommitteeMeeting
**Meeting Entity**: Committee meetings for application reviews
- `_id`: ObjectId (Primary Key)
- `programmeId`: ObjectId → FundingProgramme (required)
- `title`, `description`: String
- `date`: Date (required)
- `startTime`, `endTime`: String
- `location`: Enum ['physical', 'virtual']
- `meetingLink`: String
- `physicalAddress`: Object
- `attendees`: Array[Object] (userId, name, role, status)
- `applications`: Array[Object] (applicationId, status, recommendation)
- `agenda`, `minutes`: String
- `status`: Enum ['scheduled', 'in-progress', 'completed', 'cancelled']
- `createdAt`, `updatedAt`: Date

### 7. Scorecard & ScorecardVersion
**Assessment Entity**: Scoring system for applications
- `_id`: ObjectId (Primary Key)
- `applicationId`: String (required)
- `stageId`, `substageId`: String (required)
- `name`, `description`: String
- `criteria`: Array[Object] (scoring criteria)
- `totalScore`, `maxPossibleScore`: Number
- `status`: Enum ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'APPROVED', 'REJECTED']
- `isDraft`: Boolean
- `currentVersion`: Number
- `lastModifiedBy`, `lastModifiedAt`: String, Date
- `lockedBy`, `lockedAt`: String, Date
- `finalizedBy`, `finalizedAt`: String, Date

### 8. SiteVisit
**Assessment Entity**: On-site visits for application verification
- `_id`: ObjectId (Primary Key)
- `id`: String (required)
- `applicationId`: String (required)
- `scheduledDate`: Date (required)
- `status`: Enum ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']
- `location`: Object (address details)
- `conductedBy`: String (required)
- `findings`: String
- `checklist`: Array[Object] (verification items)
- `photos`, `documents`: Array[Object] (attachments)
- `duration`: Number (minutes)
- `actionItems`: Array[Object]
- `createdDate`, `updatedDate`: Date

## Communication & Collaboration

### 9. ChatRoom
**Communication Entity**: Chat rooms for different contexts
- `_id`: ObjectId (Primary Key)
- `id`: String (unique, sparse)
- `name`: String (required)
- `description`: String
- `type`: Enum ['DIRECT', 'GROUP', 'COMMITTEE', 'APPLICATION', 'PROGRAMME']
- `directMessageUsers`: Array[ObjectId] → User
- `relatedEntity`: Object (entityType, entityId, entityName)
- `settings`: Object (privacy, permissions)
- `lastActivity`: Date
- `lastMessage`: ObjectId → ChatMessage
- `messageCount`: Number
- `createdBy`: ObjectId → User
- `createdAt`, `updatedAt`: Date

### 10. ChatMessage
**Communication Entity**: Individual chat messages
- `_id`: ObjectId (Primary Key)
- `id`: String (unique, auto-generated: MSG-2025-XXXXXXXX)
- `roomId`: ObjectId → ChatRoom (required)
- `senderId`: ObjectId → User (required)
- `content`: Object (text, type)
- `attachments`: Array[Object] (file details)
- `voiceNote`: Object (duration, fileUrl, transcription)
- `metadata`: Object (isEdited, isDeleted, editHistory)
- `replyTo`: ObjectId → ChatMessage
- `threadMessages`: Array[ObjectId] → ChatMessage
- `mentions`: Array[Object] (userId, username)
- `readBy`: Array[Object] (userId, readAt)
- `deliveryStatus`: Object
- `reactions`: Array[Object] (emoji, users)
- `systemMessage`: Object (type, data)
- `createdAt`, `updatedAt`: Date

### 11. ChatParticipant
**Communication Entity**: User participation in chat rooms
- `_id`: ObjectId (Primary Key)
- `roomId`: ObjectId → ChatRoom (required)
- `userId`: ObjectId → User (required)
- `role`: Enum ['ADMIN', 'MODERATOR', 'MEMBER']
- `permissions`: Object (various chat permissions)
- `preferences`: Object (notification settings)
- `isActive`, `isBanned`: Boolean
- `bannedReason`, `bannedAt`: String, Date
- `bannedBy`: ObjectId → User
- `joinedAt`, `leftAt`, `lastSeenAt`: Date
- `lastReadMessageId`: ObjectId → ChatMessage
- `unreadCount`: Number
- `addedBy`: ObjectId → User
- `createdAt`, `updatedAt`: Date

## Reporting & Analytics

### 12. Report
**Analytics Entity**: System reports and analytics
- `_id`: ObjectId (Primary Key)
- `name`: String (required)
- `description`: String
- `type`: Enum ['status', 'processing-time', 'approval-rates', 'funding-distribution', 'regional-analysis', 'custom']
- `filters`: Object (date ranges, programme filters, etc.)
- `columns`: Array[Object] (field, header, dataType, etc.)
- `data`: Object (columns, rows, summary)
- `createdBy`: ObjectId → User
- `lastGeneratedAt`: Date
- `status`: Enum ['draft', 'generated', 'scheduled', 'error']
- `format`: Enum ['pdf', 'excel', 'csv']
- `isTemplate`, `isPublic`: Boolean
- `tags`: Array[String]
- `createdAt`, `updatedAt`: Date

### 13. ScheduledReport
**Analytics Entity**: Automated report scheduling
- `_id`: ObjectId (Primary Key)
- `name`, `description`: String
- `reportId`: ObjectId → Report (required)
- `schedule`: Object (frequency, timing details)
- `delivery`: Object (email recipients, format)
- `status`: Enum ['active', 'paused', 'completed', 'error']
- `createdBy`: ObjectId → User (required)
- `lastExecutionTime`, `nextExecutionTime`: Date
- `executions`: Array[Object] (execution history)
- `createdAt`, `updatedAt`: Date

### 14. Dashboard
**Analytics Entity**: User dashboards with widgets
- `_id`: ObjectId (Primary Key)
- `name`: String (required)
- `description`: String
- `widgets`: Array[Object] (widget configurations)
- `layout`: Object (columns, rowHeight)
- `isDefault`, `isPublic`: Boolean
- `createdBy`: ObjectId → User (required)
- `createdAt`, `updatedAt`: Date

## Loan Management (Extended Functionality)

### 15. LoanProduct
**Financial Entity**: Loan product definitions
- `_id`: ObjectId (Primary Key)
- `productId`: String (required, unique)
- `name`: String (required)
- `description`: String
- `category`: Enum ['BUSINESS_LOAN', 'WORKING_CAPITAL', 'EQUIPMENT_FINANCE', 'PROPERTY_FINANCE']
- `minAmount`, `maxAmount`: Number
- `minTerm`, `maxTerm`: Number
- `termUnit`: Enum ['DAYS', 'MONTHS', 'YEARS']
- `baseInterestRate`: Number
- `fees`: Object (various fee structures)
- `eligibilityCriteria`: Array[String]
- `requiredDocuments`: Array[String]
- `collateralRequired`: Boolean
- `status`: Enum ['ACTIVE', 'INACTIVE', 'DISCONTINUED']
- `createdAt`, `updatedAt`: Date

### 16. LoanOffer
**Financial Entity**: Loan offers made to applicants
- `_id`: ObjectId (Primary Key)
- `offerId`: String (required, unique)
- `applicationId`: String → Application (required)
- `productId`: String → LoanProduct (required)
- `pricingRuleId`: String → PricingRule
- `offeredAmount`, `approvedAmount`: Number
- `interestRate`: Number
- `term`: Number
- `termUnit`: Enum ['DAYS', 'MONTHS', 'YEARS']
- `repaymentFrequency`: Enum
- `monthlyPayment`, `totalRepayment`, `totalInterest`: Number
- `fees`: Object (various fees)
- `collateralRequired`: Boolean
- `collateralDetails`: Object
- `specialConditions`: Array[String]
- `repaymentSchedule`: Array[Object]
- `riskAssessment`: Object
- `status`: Enum ['DRAFT', 'PENDING', 'SENT', 'ACCEPTED', 'REJECTED', 'EXPIRED', 'WITHDRAWN']
- `validUntil`: Date
- `sentDate`, `acceptedDate`, `rejectedDate`: Date
- `rejectionReason`: String
- `approvalWorkflow`: Object
- `createdAt`, `updatedAt`: Date

## Additional Supporting Entities

### 17. Interview
**Assessment Entity**: Structured interviews with applicants
- `_id`: ObjectId (Primary Key)
- `id`: String (required)
- `applicationId`: String (required)
- `title`, `description`, `agenda`: String
- `templateId`, `templateVersion`: String
- `status`: Enum ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']
- `type`: Enum ['ONLINE', 'IN_PERSON', 'PHONE']
- `scheduledDate`: Date (required)
- `duration`: Number (minutes)
- `location`, `meetingLink`: String
- `primaryInterviewer`, `interviewee`: String
- `questions`: Array[Object]
- `notes`: Array[Object]
- `participants`: Array[Object]
- `recordings`: Array[Object]
- `sections`: Array[Object]
- `summary`, `outcome`, `followUpActions`: String
- `completedAt`: Date
- `createdAt`, `updatedAt`: Date

### 18. ProgrammeAssignment
**Assignment Entity**: User assignments to programmes
- `_id`: ObjectId (Primary Key)
- `id`: String (required, unique)
- `programmeId`: String → FundingProgramme (required)
- `assigneeType`: Enum ['user', 'serviceProvider']
- `assigneeId`: String (required)
- `role`: String (required)
- `status`: Enum ['active', 'inactive']
- `assignedBy`: String
- `assignedAt`, `updatedAt`: Date

### 19. Notification & NotificationTemplate
**Communication Entity**: System notifications
- Various notification-related schemas for user alerts and communications

### 20. Role & Permission
**Security Entity**: Role-based access control
- System roles and permissions management

## Key Relationships Summary

1. **CorporateSponsor** → **FundingProgramme** (1:N)
2. **FundingProgramme** → **Application** (1:N)
3. **Application** → **ApprovalWorkflow** (1:N)
4. **Application** → **SiteVisit** (1:N)
5. **Application** → **Scorecard** (1:N)
6. **Application** → **Interview** (1:N)
7. **Application** → **LoanOffer** (1:N)
8. **User** → **ChatMessage** (1:N)
9. **ChatRoom** → **ChatMessage** (1:N)
10. **ChatRoom** ↔ **User** (N:M via ChatParticipant)
11. **Report** → **ScheduledReport** (1:N)
12. **Report** → **Dashboard** (N:M via widgets)

## Indexes and Performance Considerations

- Text indexes on searchable fields (name, description, etc.)
- Compound indexes on frequently queried combinations
- Sparse indexes on optional unique fields
- Date-based indexes for time-series queries
- Reference indexes for foreign key relationships

## Data Integrity and Validation

- Required field validations
- Enum value constraints
- Custom validation rules
- Pre-save middleware for data processing
- Post-save middleware for related entity updates
- Unique constraints where applicable
