<?xml version="1.0" encoding="UTF-8"?>
<database_schema>
  <metadata>
    <name>Screening Portal Database Schema</name>
    <version>2.0</version>
    <description>Updated database schema with Profile and SME entities</description>
    <last_updated>2025-01-28</last_updated>
  </metadata>

  <!-- Core Business Entities -->
  <entities>
    
    <!-- Corporate Sponsor Entity -->
    <entity name="CorporateSponsor" type="primary">
      <description>Organizations that provide funding through various programmes</description>
      <fields>
        <field name="_id" type="ObjectId" primary_key="true" required="true"/>
        <field name="name" type="String" required="true"/>
        <field name="description" type="String"/>
        <field name="logo" type="String"/>
        <field name="contactPerson" type="Object"/>
        <field name="address" type="Object"/>
        <field name="website" type="String"/>
        <field name="industry" type="String"/>
        <field name="totalFunding" type="Number" default="0"/>
        <field name="activePrograms" type="Number" default="0"/>
        <field name="totalBeneficiaries" type="Number" default="0"/>
        <field name="status" type="Enum" values="active,inactive" default="active"/>
        <field name="createdAt" type="Date" default="now"/>
        <field name="updatedAt" type="Date" default="now"/>
      </fields>
      <indexes>
        <index type="text" fields="name,description,industry"/>
      </indexes>
    </entity>

    <!-- SME Entity -->
    <entity name="SME" type="primary">
      <description>Small and Medium Enterprises that apply for funding</description>
      <fields>
        <field name="_id" type="ObjectId" primary_key="true" required="true"/>
        <field name="id" type="String" unique="true" required="true"/>
        <field name="businessName" type="String" required="true"/>
        <field name="registrationNumber" type="String" unique="true"/>
        <field name="businessType" type="Enum" values="Sole Proprietorship,Partnership,Private Company,Public Company,Close Corporation,Trust,NPO,Other"/>
        <field name="industry" type="String"/>
        <field name="sector" type="String"/>
        <field name="businessDescription" type="String"/>
        <field name="yearEstablished" type="Number"/>
        <field name="numberOfEmployees" type="Number"/>
        <field name="annualTurnover" type="Number"/>
        <field name="businessAddress" type="Object"/>
        <field name="contactDetails" type="Object"/>
        <field name="bankingDetails" type="Object"/>
        <field name="taxDetails" type="Object"/>
        <field name="bbbeeLevel" type="String"/>
        <field name="bbbeeCertificateExpiryDate" type="Date"/>
        <field name="blackOwnershipPercentage" type="Number"/>
        <field name="blackWomenOwnershipPercentage" type="Number"/>
        <field name="isExxaroSupplier" type="Boolean" default="false"/>
        <field name="corporateSponsorId" type="ObjectId" foreign_key="CorporateSponsor._id"/>
        <field name="status" type="Enum" values="active,inactive,suspended" default="active"/>
        <field name="verificationStatus" type="Enum" values="pending,verified,rejected" default="pending"/>
        <field name="documents" type="Array"/>
        <field name="notes" type="Array"/>
        <field name="tags" type="Array"/>
        <field name="createdAt" type="Date" default="now"/>
        <field name="updatedAt" type="Date" default="now"/>
      </fields>
      <indexes>
        <index type="text" fields="businessName,businessDescription,industry,sector"/>
        <index type="single" fields="registrationNumber"/>
        <index type="single" fields="corporateSponsorId"/>
      </indexes>
    </entity>

    <!-- Profile Entity -->
    <entity name="Profile" type="primary">
      <description>User profiles containing personal and professional information</description>
      <fields>
        <field name="_id" type="ObjectId" primary_key="true" required="true"/>
        <field name="id" type="String" unique="true" required="true"/>
        <field name="userId" type="ObjectId" foreign_key="User._id" required="true"/>
        <field name="smeId" type="ObjectId" foreign_key="SME._id"/>
        <field name="corporateSponsorId" type="ObjectId" foreign_key="CorporateSponsor._id"/>
        <field name="profileType" type="Enum" values="individual,business_owner,corporate_representative,analyst,manager" required="true"/>
        <field name="firstName" type="String" required="true"/>
        <field name="lastName" type="String" required="true"/>
        <field name="middleName" type="String"/>
        <field name="idNumber" type="String" unique="true"/>
        <field name="passportNumber" type="String"/>
        <field name="dateOfBirth" type="Date"/>
        <field name="gender" type="Enum" values="male,female,other,prefer_not_to_say"/>
        <field name="nationality" type="String"/>
        <field name="race" type="Enum" values="African,Coloured,Indian,White,Other"/>
        <field name="maritalStatus" type="Enum" values="single,married,divorced,widowed,other"/>
        <field name="contactDetails" type="Object"/>
        <field name="physicalAddress" type="Object"/>
        <field name="postalAddress" type="Object"/>
        <field name="emergencyContact" type="Object"/>
        <field name="education" type="Array"/>
        <field name="workExperience" type="Array"/>
        <field name="skills" type="Array"/>
        <field name="certifications" type="Array"/>
        <field name="languages" type="Array"/>
        <field name="profilePicture" type="String"/>
        <field name="documents" type="Array"/>
        <field name="preferences" type="Object"/>
        <field name="privacySettings" type="Object"/>
        <field name="isVerified" type="Boolean" default="false"/>
        <field name="verificationDate" type="Date"/>
        <field name="verifiedBy" type="ObjectId" foreign_key="User._id"/>
        <field name="status" type="Enum" values="active,inactive,suspended" default="active"/>
        <field name="createdAt" type="Date" default="now"/>
        <field name="updatedAt" type="Date" default="now"/>
      </fields>
      <indexes>
        <index type="single" fields="userId" unique="true"/>
        <index type="single" fields="smeId"/>
        <index type="single" fields="corporateSponsorId"/>
        <index type="single" fields="idNumber"/>
        <index type="text" fields="firstName,lastName,middleName"/>
      </indexes>
    </entity>

    <!-- User Entity (Updated) -->
    <entity name="User" type="primary">
      <description>System users with authentication and role management</description>
      <fields>
        <field name="_id" type="ObjectId" primary_key="true" required="true"/>
        <field name="id" type="String" unique="true"/>
        <field name="username" type="String" required="true" unique="true"/>
        <field name="email" type="String" required="true" unique="true"/>
        <field name="password" type="String" required="true"/>
        <field name="role" type="Enum" values="admin,analyst,manager,committee_member,corporate_user,sme_owner"/>
        <field name="roles" type="Array"/>
        <field name="permissions" type="Array"/>
        <field name="organizationType" type="Enum" values="20/20Insight,CorporateSponsor,ServiceProvider,SME"/>
        <field name="organizationId" type="String"/>
        <field name="corporateSponsorId" type="ObjectId" foreign_key="CorporateSponsor._id"/>
        <field name="fundingProgrammeId" type="ObjectId" foreign_key="FundingProgramme._id"/>
        <field name="programmeAssignments" type="Array"/>
        <field name="phone" type="String"/>
        <field name="profilePicture" type="String"/>
        <field name="status" type="Enum" values="active,inactive,suspended" default="active"/>
        <field name="isActive" type="Boolean" default="true"/>
        <field name="lastLogin" type="Date"/>
        <field name="resetPasswordToken" type="String"/>
        <field name="resetPasswordExpires" type="Date"/>
        <field name="createdAt" type="Date" default="now"/>
        <field name="updatedAt" type="Date" default="now"/>
      </fields>
      <indexes>
        <index type="single" fields="username" unique="true"/>
        <index type="single" fields="email" unique="true"/>
        <index type="single" fields="corporateSponsorId"/>
        <index type="single" fields="fundingProgrammeId"/>
      </indexes>
    </entity>

    <!-- Funding Programme Entity -->
    <entity name="FundingProgramme" type="primary">
      <description>Funding programs offered by corporate sponsors</description>
      <fields>
        <field name="_id" type="ObjectId" primary_key="true" required="true"/>
        <field name="name" type="String" required="true"/>
        <field name="description" type="String"/>
        <field name="corporateSponsorId" type="ObjectId" foreign_key="CorporateSponsor._id" required="true"/>
        <field name="objectives" type="Array"/>
        <field name="fundingCriteria" type="Array"/>
        <field name="fundingTerms" type="String"/>
        <field name="fundingTypes" type="Array"/>
        <field name="eligibilityCriteria" type="Array"/>
        <field name="geographicalCoverage" type="Object"/>
        <field name="sectors" type="Object"/>
        <field name="fundingPurposes" type="Array"/>
        <field name="budget" type="Object"/>
        <field name="timeline" type="Object"/>
        <field name="status" type="Enum" values="draft,active,paused,completed,cancelled" default="draft"/>
        <field name="createdBy" type="ObjectId" foreign_key="User._id"/>
        <field name="createdAt" type="Date" default="now"/>
        <field name="updatedAt" type="Date" default="now"/>
      </fields>
      <indexes>
        <index type="text" fields="name,description,objectives,fundingCriteria,eligibilityCriteria"/>
        <index type="single" fields="corporateSponsorId"/>
      </indexes>
    </entity>

    <!-- Application Entity (Updated) -->
    <entity name="Application" type="primary">
      <description>Funding applications submitted by SMEs</description>
      <fields>
        <field name="_id" type="ObjectId" primary_key="true" required="true"/>
        <field name="id" type="String" required="true" unique="true"/>
        <field name="programmeId" type="ObjectId" foreign_key="FundingProgramme._id" required="true"/>
        <field name="corporateSponsorId" type="ObjectId" foreign_key="CorporateSponsor._id" required="true"/>
        <field name="smeId" type="ObjectId" foreign_key="SME._id" required="true"/>
        <field name="profileId" type="ObjectId" foreign_key="Profile._id" required="true"/>
        <field name="registrationNumber" type="String"/>
        <field name="fundingAmount" type="Number"/>
        <field name="submissionDate" type="Date" default="now"/>
        <field name="currentMainStage" type="Enum" default="ONBOARDING"/>
        <field name="currentSubStage" type="Enum" default="BENEFICIARY_REGISTRATION"/>
        <field name="currentStatus" type="Enum"/>
        <field name="owner" type="String"/>
        <field name="personalInfo" type="Object"/>
        <field name="businessInfo" type="Object"/>
        <field name="contactDetails" type="Object"/>
        <field name="financialInfo" type="Object"/>
        <field name="bbbeeProfile" type="Object"/>
        <field name="smeInterview" type="Object"/>
        <field name="documents" type="Array"/>
        <field name="notes" type="Array"/>
        <field name="tags" type="Array"/>
        <field name="stageStatusAuditLog" type="Array"/>
        <field name="score" type="Number"/>
        <field name="assignedTo" type="String"/>
        <field name="approvalWorkflow" type="Object"/>
        <field name="lastUpdated" type="Date" default="now"/>
      </fields>
      <indexes>
        <index type="single" fields="programmeId"/>
        <index type="single" fields="corporateSponsorId"/>
        <index type="single" fields="smeId"/>
        <index type="single" fields="profileId"/>
        <index type="text" fields="id,registrationNumber"/>
      </indexes>
    </entity>

  </entities>

  <!-- Relationships -->
  <relationships>
    
    <!-- User to Profile (One-to-One) -->
    <relationship type="one_to_one">
      <from entity="User" field="_id"/>
      <to entity="Profile" field="userId"/>
      <description>Each user has exactly one profile</description>
    </relationship>

    <!-- Profile to SME (Many-to-One) -->
    <relationship type="many_to_one">
      <from entity="Profile" field="smeId"/>
      <to entity="SME" field="_id"/>
      <description>Multiple profiles can be associated with one SME (business owners, employees, etc.)</description>
    </relationship>

    <!-- Profile to CorporateSponsor (Many-to-One) -->
    <relationship type="many_to_one">
      <from entity="Profile" field="corporateSponsorId"/>
      <to entity="CorporateSponsor" field="_id"/>
      <description>Multiple profiles can be associated with one corporate sponsor (employees, representatives)</description>
    </relationship>

    <!-- SME to CorporateSponsor (Many-to-One) -->
    <relationship type="many_to_one">
      <from entity="SME" field="corporateSponsorId"/>
      <to entity="CorporateSponsor" field="_id"/>
      <description>SMEs can be associated with corporate sponsors (suppliers, partners)</description>
    </relationship>

    <!-- CorporateSponsor to FundingProgramme (One-to-Many) -->
    <relationship type="one_to_many">
      <from entity="CorporateSponsor" field="_id"/>
      <to entity="FundingProgramme" field="corporateSponsorId"/>
      <description>Corporate sponsors can have multiple funding programmes</description>
    </relationship>

    <!-- SME to Application (One-to-Many) -->
    <relationship type="one_to_many">
      <from entity="SME" field="_id"/>
      <to entity="Application" field="smeId"/>
      <description>SMEs can submit multiple applications</description>
    </relationship>

    <!-- Profile to Application (One-to-Many) -->
    <relationship type="one_to_many">
      <from entity="Profile" field="_id"/>
      <to entity="Application" field="profileId"/>
      <description>Profiles can be associated with multiple applications</description>
    </relationship>

    <!-- FundingProgramme to Application (One-to-Many) -->
    <relationship type="one_to_many">
      <from entity="FundingProgramme" field="_id"/>
      <to entity="Application" field="programmeId"/>
      <description>Funding programmes can have multiple applications</description>
    </relationship>

    <!-- User to FundingProgramme (Many-to-One) -->
    <relationship type="many_to_one">
      <from entity="User" field="fundingProgrammeId"/>
      <to entity="FundingProgramme" field="_id"/>
      <description>Users can be assigned to manage funding programmes</description>
    </relationship>

    <!-- User to CorporateSponsor (Many-to-One) -->
    <relationship type="many_to_one">
      <from entity="User" field="corporateSponsorId"/>
      <to entity="CorporateSponsor" field="_id"/>
      <description>Users can be associated with corporate sponsors</description>
    </relationship>

  </relationships>

  <!-- Business Rules -->
  <business_rules>
    <rule>
      <name>Profile-User Uniqueness</name>
      <description>Each user must have exactly one profile, and each profile belongs to exactly one user</description>
    </rule>
    
    <rule>
      <name>Profile-Entity Association</name>
      <description>A profile must be associated with either an SME or a Corporate Sponsor, but not both</description>
    </rule>
    
    <rule>
      <name>Application Requirements</name>
      <description>Every application must be linked to an SME, a Profile, a Funding Programme, and a Corporate Sponsor</description>
    </rule>
    
    <rule>
      <name>SME-CorporateSponsor Association</name>
      <description>SMEs can optionally be associated with Corporate Sponsors (for supplier relationships)</description>
    </rule>
  </business_rules>

</database_schema>
